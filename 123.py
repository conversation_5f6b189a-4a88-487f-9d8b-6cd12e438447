import tushare as ts
import pandas as pd
from datetime import datetime, timedelta

# 设置Tushare token（需要注册获取）
# ts.set_token('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')  # 请替换为你的token
pro = ts.pro_api('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')

# 格力电器股票代码
stock_code = '000651.SZ'  # 格力电器

def get_gree_financial_data():
    """
    获取格力电器的财务数据
    """
    
    try:
        print("正在获取格力电器财务数据...")
        
        # 1. 获取基本信息
        basic_info = pro.stock_basic(ts_code=stock_code, fields='ts_code,name,industry,market,list_date')
        print(f"股票信息: {basic_info.iloc[0]['name']} ({basic_info.iloc[0]['ts_code']})")
        print(f"所属行业: {basic_info.iloc[0]['industry']}")
        
        # 2. 获取最新财务数据 - 资产负债表
        balance_sheet = pro.balancesheet(
            ts_code=stock_code,
            start_date='20240101',  # 从2024年开始获取
            fields='ts_code,ann_date,f_ann_date,end_date,total_assets,total_liab,total_hldr_eqy_exc_min_int'
        )
        
        # 3. 获取利润表数据
        income_statement = pro.income(
            ts_code=stock_code,
            start_date='20240101',
            fields='ts_code,ann_date,f_ann_date,end_date,revenue,n_income,n_income_attr_p'
        )
        
        # 4. 获取现金流量表数据
        cashflow_statement = pro.cashflow(
            ts_code=stock_code,
            start_date='20240101',
            fields='ts_code,ann_date,f_ann_date,end_date,n_cashflow_act,c_fr_sale_sg,c_paid_goods_s'
        )
        
        # 5. 获取财务指标数据（包含TTM数据）
        financial_indicators = pro.fina_indicator(
            ts_code=stock_code,
            start_date='20240101',
            fields='ts_code,ann_date,end_date,roe,roa,netprofit_margin,current_ratio,quick_ratio'
        )
        
        # 数据处理和展示
        print("\n=== 最新资产负债表数据 ===")
        if not balance_sheet.empty:
            latest_balance = balance_sheet.iloc[0]
            print(f"报告期: {latest_balance['end_date']}")
            print(f"总资产: {latest_balance['total_assets']:,.0f} 万元")
            print(f"总负债: {latest_balance['total_liab']:,.0f} 万元")  
            print(f"净资产(股东权益): {latest_balance['total_hldr_eqy_exc_min_int']:,.0f} 万元")
        
        print("\n=== 最新利润表数据 ===")
        if not income_statement.empty:
            latest_income = income_statement.iloc[0]
            print(f"报告期: {latest_income['end_date']}")
            print(f"营业收入: {latest_income['revenue']:,.0f} 万元")
            print(f"净利润: {latest_income['n_income']:,.0f} 万元")
            print(f"归母净利润: {latest_income['n_income_attr_p']:,.0f} 万元")
        
        print("\n=== 最新现金流量表数据 ===")
        if not cashflow_statement.empty:
            latest_cashflow = cashflow_statement.iloc[0]
            print(f"报告期: {latest_cashflow['end_date']}")
            print(f"经营现金流净额: {latest_cashflow['n_cashflow_act']:,.0f} 万元")
        
        # 计算TTM数据（最近四个季度）
        print("\n=== TTM数据计算 ===")
        if len(income_statement) >= 4:
            ttm_revenue = income_statement.head(4)['revenue'].sum()
            ttm_net_income = income_statement.head(4)['n_income'].sum()
            print(f"营业收入TTM: {ttm_revenue:,.0f} 万元")
            print(f"净利润TTM: {ttm_net_income:,.0f} 万元")
        
        if len(cashflow_statement) >= 4:
            ttm_operating_cf = cashflow_statement.head(4)['n_cashflow_act'].sum()
            print(f"经营现金流TTM: {ttm_operating_cf:,.0f} 万元")
        
        # 返回数据框供进一步分析
        return {
            'balance_sheet': balance_sheet,
            'income_statement': income_statement,
            'cashflow_statement': cashflow_statement,
            'financial_indicators': financial_indicators
        }
        
    except Exception as e:
        print(f"获取数据时出错: {str(e)}")
        print("请检查:")
        print("1. 是否已安装tushare: pip install tushare")
        print("2. 是否已设置token: ts.set_token('your_token')")
        print("3. token是否有效且有足够积分")
        return None

def get_daily_basic_data():
    """
    获取最近的日线基本信息
    """
    try:
        # 获取最近交易日的基本面数据
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        daily_basic = pro.daily_basic(
            ts_code=stock_code,
            start_date=start_date,
            end_date=end_date,
            fields='ts_code,trade_date,pe,pe_ttm,pb,ps,ps_ttm,total_mv,circ_mv'
        )
        
        if not daily_basic.empty:
            print("\n=== 最新市场估值数据 ===")
            latest_data = daily_basic.iloc[0]
            print(f"交易日期: {latest_data['trade_date']}")
            print(f"PE(TTM): {latest_data['pe_ttm']:.2f}")
            print(f"PB: {latest_data['pb']:.2f}")
            print(f"总市值: {latest_data['total_mv']:,.0f} 万元")
            print(f"流通市值: {latest_data['circ_mv']:,.0f} 万元")
        
        return daily_basic
        
    except Exception as e:
        print(f"获取日线数据时出错: {str(e)}")
        return None

# 使用示例
if __name__ == "__main__":
    print("格力电器财务数据获取工具")
    print("=" * 50)
    
    # 注意：需要先设置你的tushare token
    print("⚠️ 使用前请先设置Tushare token:")
    print("1. 注册 https://tushare.pro")
    print("2. 获取token")
    print("3. 运行: ts.set_token('your_token_here')")
    print()
    
    # 获取财务数据
    financial_data = get_gree_financial_data()
    
    # 获取市场数据
    market_data = get_daily_basic_data()
    
    print("\n" + "=" * 50)
    print("数据获取完成！")
    
    # 如果需要导出到Excel
    if financial_data:
        print("\n如需导出数据到Excel，可以使用:")
        print("financial_data['balance_sheet'].to_excel('格力电器_资产负债表.xlsx')")
        print("financial_data['income_statement'].to_excel('格力电器_利润表.xlsx')")
        print("financial_data['cashflow_statement'].to_excel('格力电器_现金流量表.xlsx')")